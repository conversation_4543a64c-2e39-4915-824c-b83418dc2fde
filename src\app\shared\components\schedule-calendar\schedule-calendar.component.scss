:host {
  display: block;
  height: 100%;
  min-height: 900px; /* Ensure minimum height for full calendar display */
}

.calendar-container {
  height: 100%;
  min-height: 900px; /* Ensure enough space for 24 hours */
  overflow: visible; /* Allow hour labels to be visible */
  position: relative;
}

.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions */
button {
  transition: all 0.2s ease;
}

/* Floating button */
.fixed {
  transition: all 0.2s ease;
}

.fixed:hover {
  transform: scale(1.05);
}

/* Schedule event animations */
.schedule-event {
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.schedule-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ensure proper text rendering */
.event-title {
  line-height: 1.2;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.event-time {
  line-height: 1.3;
  font-variant-numeric: tabular-nums;
}

/* Time slot hover effects */
.time-slot:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Fixed hour labels */
.fixed-hour-labels {
  background-color: white;
  border-right: 1px solid #e5e7eb;
  z-index: 20;
  overflow: visible;
  position: relative;
}

/* Hour labels container improvements */
.calendar-container .absolute.left-0.top-0.bottom-0.w-12 {
  overflow: visible !important;
  z-index: 25 !important;

  .relative.h-full {
    overflow: visible !important;
    min-height: 800px !important;

    > div {
      overflow: visible !important;
    }
  }
}

/* Scrollable content area */
.scrollable-content {
  padding-left: 3rem; /* 48px width of hour labels */
}

/* Day selector enhancements */
.day-button {
  transition: all 0.2s ease;
}

.day-button:hover {
  transform: translateY(-1px);
}

.day-button.active {
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Current time indicator animation */
.current-time-dot {
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

/* Hour label improvements */
.hour-label {
  font-variant-numeric: tabular-nums;
  line-height: 1;
}

/* Empty state styling */
.empty-state {
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.empty-state:hover {
  opacity: 1;
}

/* Header gradient */
.calendar-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Event status colors */
.event-active {
  border-left-color: #10b981 !important;
  background-color: rgba(16, 185, 129, 0.05) !important;
  border-color: rgba(16, 185, 129, 0.2) !important;
}

.event-inactive {
  border-left-color: #6b7280 !important;
  background-color: rgba(107, 114, 128, 0.05) !important;
  border-color: rgba(107, 114, 128, 0.2) !important;
}

.event-completed {
  border-left-color: #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.05) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

/* Calendar event improvements */
.calendar-event {
  min-height: 40px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .event-title {
    font-weight: 600;
    color: #1f2937;
  }

  .event-time {
    color: #6b7280;
    font-weight: 500;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .day-selector {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .day-selector::-webkit-scrollbar {
    display: none;
  }

  .floating-button {
    bottom: 1rem;
    right: 1rem;
  }

  .calendar-event {
    min-width: 180px;
    padding: 8px;

    .event-time {
      font-size: 11px;
    }
  }

  .hour-label {
    font-size: 10px;
    width: 40px;
  }
}

/* Ensure proper z-index stacking */
.calendar-container {
  position: relative;
  z-index: 1;
}

/* Fix for hour labels positioning */
.hour-label {
  z-index: 10;
  pointer-events: none;
}

/* Fix text rendering issues */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Ensure proper event positioning and text display */
.calendar-event {
  position: absolute;
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: visible; // Allow content to be visible
  transition: all 0.2s ease;
  min-height: 80px; // Ensure minimum height for proper content display
  min-width: 200px; // Ensure minimum width for proper time display
  width: auto; // Allow width to adjust based on content
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
  
  // Show resize handles on hover
  &:hover .resize-handle-top,
  &:hover .resize-handle-bottom {
    opacity: 1;
  }
  
  .event-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 60px; // Ensure minimum height for proper time display
    overflow: visible !important;
    
    .event-header {
      flex: 1;
      min-height: auto !important;
      overflow: visible !important;
      
      .event-title {
        font-weight: 600;
        font-size: 14px;
        line-height: 1.3;
        color: #111827;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .event-time {
        font-size: 12px;
        color: #6b7280;
        line-height: 1.4;
        margin-bottom: 8px;
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        font-variant-numeric: tabular-nums;
        font-weight: 500;
        min-width: fit-content !important;
        width: 100% !important;
        max-width: none !important;
        display: block !important;
        word-break: keep-all !important;
        flex-shrink: 0 !important;
      }
    }
    
    .event-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 8px;
      
      .event-status {
        font-size: 11px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 12px;
        display: inline-flex;
        align-items: center;
        
        .status-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 4px;
        }
      }
      
      .event-actions {
        display: flex;
        gap: 4px;
        opacity: 1; // Always visible
        transition: opacity 0.2s ease;
        
        button {
          padding: 4px;
          border-radius: 4px;
          border: none;
          background: transparent;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #f3f4f6;
            transform: scale(1.1);
          }
          
          .material-icons {
            font-size: 14px;
          }
        }
      }
    }
  }
  
  &:hover .event-actions {
    opacity: 1;
  }
}

// Resize handles
.resize-handle-top,
.resize-handle-bottom {
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease, height 0.2s ease;
}

/* Prevent text selection issues */
.calendar-grid {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  position: relative;
}

/* Ensure Material Icons render properly */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 16px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.resize-handle-top {
  position: absolute;
  top: -8px;
  left: -4px;
  right: -4px;
  height: 16px;
  cursor: ns-resize;
  z-index: 20;
  background-color: rgba(59, 130, 246, 0.4);
  border-top: 3px solid #3b82f6;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.resize-handle-bottom {
  position: absolute;
  bottom: -8px;
  left: -4px;
  right: -4px;
  height: 16px;
  cursor: ns-resize;
  z-index: 20;
  background-color: rgba(59, 130, 246, 0.4);
  border-bottom: 3px solid #3b82f6;
  border-radius: 0 0 8px 8px;
  transition: all 0.3s ease;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0;
  // Ensure the handle is above other elements
  z-index: 30;
}

.resize-handle-top:hover,
.resize-handle-bottom:hover {
  background-color: rgba(37, 99, 235, 0.7);
  border-top-color: #2563eb;
  border-bottom-color: #2563eb;
  height: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

// Show handles when event is hovered or when resizing
.calendar-event:hover .resize-handle-top,
.calendar-event:hover .resize-handle-bottom,
.calendar-event.resizing .resize-handle-top,
.calendar-event.resizing .resize-handle-bottom {
  opacity: 1;
}

// Ensure handles are always clickable
.resize-handle-top,
.resize-handle-bottom {
  pointer-events: auto;
}

.resize-handle-top::after,
.resize-handle-bottom::after {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 3px;
  background-color: white;
  border-radius: 2px;
  opacity: 1;
}

.resize-handle-top::after {
  top: 50%;
  transform: translate(-50%, -50%);
}

.resize-handle-bottom::after {
  bottom: 50%;
  transform: translate(-50%, 50%);
}

/* Drag and drop feedback for calendar */
.calendar-container.drag-over {
  background-color: rgba(59, 130, 246, 0.05);
  border: 2px dashed #3b82f6;
}

.calendar-container.drag-over .calendar-grid {
  background-color: rgba(59, 130, 246, 0.03);
}

/* Visual indicator for drop position */
.drop-indicator {
  position: absolute;
  left: 48px;
  right: 16px;
  height: 2px;
  background-color: #3b82f6;
  z-index: 100;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
  border-radius: 1px;
}