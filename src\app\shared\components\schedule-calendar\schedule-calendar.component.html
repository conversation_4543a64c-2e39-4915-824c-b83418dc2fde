<div class="calendar-container flex flex-col bg-white">
  <!-- Header -->
  <div class="flex-shrink-0 p-4 border-b border-gray-200 bg-gray-50">
    <!-- Day Selector -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <button 
          (click)="navigateWeek(-1)"
          class="p-2 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <span class="material-icons text-gray-600">chevron_left</span>
        </button>
        
        <div class="flex gap-1">
          <button 
            *ngFor="let day of dayOptions; let i = index"
            (click)="selectDay(i)"
            class="px-3 py-2 text-sm font-medium rounded-lg transition-all"
            [class]="currentDay === i ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100 border'"
          >
            <div class="text-center">
              <div class="text-xs opacity-75">{{ getDayDate(i) }}</div>
              <div>{{ day }}</div>
            </div>
          </button>
        </div>
        
        <button 
          (click)="navigateWeek(1)"
          class="p-2 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <span class="material-icons text-gray-600">chevron_right</span>
        </button>
      </div>
      <button
      (click)="addSchedule.emit()"
      class="flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
    >
      <span class="material-icons mr-2">add</span>
      Add Schedule
    </button>
    </div>
  </div>
  
  <!-- Calendar Grid -->
  <div class="flex-1 relative overflow-hidden">
    <!-- Fixed Hour Labels -->
    <div class="absolute left-0 top-0 bottom-0 w-12 z-20 bg-white border-r border-gray-200 overflow-visible">
      <div class="relative h-full overflow-visible" style="min-height: 800px;">
        <div *ngFor="let hour of hours; let i = index"
             class="absolute left-0 right-0 overflow-visible"
             [style.top.px]="i * (800 / 24)"
             [style.height.px]="(800 / 24)">
          <!-- Hour Label -->
          <div class="text-xs font-medium text-gray-600 bg-white px-1 py-0.5 rounded text-right absolute right-1"
               [style.top]="'50%'"
               [style.transform]="'translateY(-50%)'">
            {{ formatHour(i) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Content Area (no scrolling) -->
    <div class="absolute inset-0 pl-12">
      <div class="relative overflow-visible" style="height: 800px; min-height: 800px;"
           (dragover)="onCalendarDragOver($event)"
           (dragleave)="onCalendarDragLeave($event)"
           (drop)="onCalendarDrop($event)">
        <!-- Hour Grid Lines (without labels) -->
        <div *ngFor="let hour of hours; let i = index"
             class="absolute left-0 right-0 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
             [style.top.px]="i * (800 / 24)"
             [style.height.px]="(800 / 24)"
             (click)="onTimeSlotClick(i)">
          <!-- Half-hour line -->
          <div class="absolute left-0 right-0 h-px bg-gray-100" [style.top.px]="(800 / 24) / 2"></div>
        </div>
        
        <!-- Current Time Indicator -->
        <div
          *ngIf="currentTimePosition >= 0"
          class="absolute left-0 right-0 z-20"
          [style.top.px]="currentTimePosition"
        >
          <div class="relative">
            <!-- Time indicator line -->
            <div class="absolute left-12 right-0 h-0.5 bg-red-500"></div>
            <!-- Time indicator dot -->
            <div class="absolute left-10 -top-1.5 w-3 h-3 bg-red-500 rounded-full current-time-dot"></div>
            <!-- Time label -->
            <div class="absolute left-0 -top-3 bg-red-500 text-white text-xs px-2 py-1 rounded font-medium whitespace-nowrap">
              {{ getCurrentTimeString() }}
            </div>
          </div>
        </div>
        
        <!-- Schedule Events -->
        <div
          *ngFor="let event of getEventsForSelectedDay(); trackBy: trackByEventId"
          class="absolute rounded-lg p-3 cursor-pointer transition-all z-10 border-2 group calendar-event shadow-sm"
          [class.resizing]="isResizing && resizingEvent?.id === event.id"
          [class.event-active]="event.status === 'active'"
          [class.event-inactive]="event.status === 'inactive'"
          [class.event-completed]="event.status === 'completed'"
          [attr.data-event-id]="event.id"
          [style.left.px]="50"
          [style.right.px]="8"
          [style.top.%]="getEventTopPositionPercent(event.startTime)"
          [style.height.%]="getEventHeightPercent(event.startTime, event.endTime)"
          [style.backgroundColor]="'#ffffff'"
          [style.borderLeftColor]="getEventColor(event.status)"
          [style.borderLeftWidth.px]="4"
          (click)="onEventClick(event)"
        >
          <div *ngIf="editingEventId !== event.id">
            <div
              class="resize-handle-top"
              title="Drag to resize schedule start time"
              (mousedown)="onResizeStart($event, event, 'top')">
            </div>
            <div class="h-full flex flex-col justify-between event-content">
              <div class="flex-1 event-header">
                <div class="font-semibold text-sm text-gray-900 truncate mb-1 event-title">{{ event.title }}</div>
                <div class="text-xs text-gray-600 mb-2 event-time">
                  {{ formatTime(event.startTime) }} - {{ formatTime(event.endTime) }}
                </div>
                <!-- Display days of week if available -->
                <div class="text-xs text-gray-500 mb-1" *ngIf="event.daysOfWeek && event.daysOfWeek.length > 0">
                  {{ getDaysOfWeekText(event.daysOfWeek) }}
                  <button
                    (click)="startEditingDays(event, $event)"
                    class="ml-1 text-blue-600 hover:text-blue-800"
                    title="Edit Days"
                  >
                    <span class="material-icons text-xs">edit</span>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between mt-2 event-footer">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium event-status"
                      [style.backgroundColor]="getStatusBadgeColor(event.status) + '20'"
                      [style.color]="getStatusBadgeColor(event.status)">
                  <span class="w-1.5 h-1.5 rounded-full mr-1 status-dot"
                        [style.backgroundColor]="getStatusBadgeColor(event.status)"></span>
                  {{ event.status.charAt(0).toUpperCase() + event.status.slice(1) }}
                </span>
                <div class="flex space-x-1 event-actions">
                  <button
                    (click)="onDeleteClick(event, $event)"
                    class="p-1 rounded hover:bg-red-100 transition-colors"
                    title="Delete Schedule"
                  >
                    <span class="material-icons text-xs text-red-600">delete</span>
                  </button>
                </div>
              </div>
            </div>
            <div
              class="resize-handle-bottom"
              title="Drag to resize schedule end time"
              (mousedown)="onResizeStart($event, event, 'bottom')">
            </div>
          </div>
          <!-- Editing UI -->
          <div *ngIf="editingEventId === event.id" class="h-full flex flex-col justify-between overflow-hidden">
            <div>
              <div class="font-semibold text-sm text-gray-900 truncate mb-2">Edit Days</div>
              <div class="flex flex-wrap gap-2">
                <label *ngFor="let day of dayOptions; let i = index" class="flex items-center space-x-2 text-sm">
                  <input type="checkbox" [checked]="editedDays.includes(i)" (change)="onDaySelectionChange(i, $event)">
                  <span>{{ day }}</span>
                </label>
              </div>
            </div>
            <div class="flex items-center justify-end space-x-2 mt-4">
              <button (click)="cancelEditing()" class="px-3 py-1 text-sm rounded bg-gray-200 hover:bg-gray-300">Cancel</button>
              <button (click)="saveDays(event)" class="px-3 py-1 text-sm rounded bg-blue-600 text-white hover:bg-blue-700">Save</button>
            </div>
          </div>
        </div>
        
        <!-- Display warning for events with no days selected -->
        <div
          *ngFor="let event of getEventsWithoutDays(); trackBy: trackByEventId"
          class="absolute rounded-lg p-3 cursor-pointer transition-all z-10 border group calendar-event"
          [class.resizing]="isResizing && resizingEvent?.id === event.id"
          [attr.data-event-id]="event.id"
          [style.left.px]="48"
          [style.right.px]="8"
          [style.top.%]="getEventTopPositionPercent(event.startTime)"
          [style.height.%]="getEventHeightPercent(event.startTime, event.endTime)"
          [style.borderLeftColor]="'#f59e0b'"
          [style.borderLeftWidth.px]="4"
          [style.backgroundColor]="'#fef3c7'"
          (click)="onEventClick(event)"
        >
          <div
            class="resize-handle-top"
            title="Drag to resize schedule start time"
            (mousedown)="onResizeStart($event, event, 'top')">
          </div>
          <div class="h-full flex flex-col justify-between event-content">
            <div class="flex-1 event-header">
              <div class="font-semibold text-sm text-gray-900 truncate mb-1 event-title">{{ event.title }}</div>
              <div class="text-xs text-gray-600 mb-2 event-time">
                {{ formatTime(event.startTime) }} - {{ formatTime(event.endTime) }}
              </div>
              <div class="text-xs text-yellow-700 font-medium flex items-center">
                <span class="material-icons text-yellow-500 mr-1 text-sm">warning</span>
                No days selected
                <button
                  (click)="startEditingDays(event, $event)"
                  class="ml-1 text-blue-600 hover:text-blue-800"
                  title="Edit Days"
                >
                  <span class="material-icons text-xs">edit</span>
                </button>
              </div>
            </div>
            <div class="flex items-center justify-between mt-2 event-footer">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800 event-status">
                <span class="w-1.5 h-1.5 rounded-full mr-1 bg-yellow-800 status-dot"></span>
                Warning
              </span>
              <div class="flex space-x-1 event-actions">
                <button
                  (click)="onDeleteClick(event, $event)"
                  class="p-1 rounded hover:bg-red-100 transition-colors"
                  title="Delete Schedule"
                >
                  <span class="material-icons text-xs text-red-600">delete</span>
                </button>
              </div>
            </div>
          </div>
          <div
            class="resize-handle-bottom"
            title="Drag to resize schedule end time"
            (mousedown)="onResizeStart($event, event, 'bottom')">
          </div>
        </div>
        
        <!-- Empty State -->
        <div *ngIf="getEventsForSelectedDay().length === 0 && getEventsWithoutDays().length === 0" 
             class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="material-icons text-xl text-gray-400">event_note</span>
            </div>
            <h3 class="text-base font-medium text-gray-900 mb-2">
              No schedules for {{ dayOptions[currentDay] }}
              <span *ngIf="selectedScreenName"> on {{ selectedScreenName }}</span>
            </h3>
            <p class="text-sm text-gray-500 mb-4">
              <span *ngIf="selectedScreenName">This screen has no scheduled content for this day. </span>
              Click on a time slot or use the button above to add a schedule
            </p>
          </div>
        </div>
        
        <!-- Warning for events with no days selected -->
        <div *ngIf="getEventsWithoutDays().length > 0 && getEventsForSelectedDay().length === 0"
             class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span class="material-icons text-xl text-yellow-500">warning</span>
            </div>
            <h3 class="text-base font-medium text-gray-900 mb-2">
              Schedules without days selected
            </h3>
            <p class="text-sm text-gray-500 mb-4">
              You have {{ getEventsWithoutDays().length }} schedule(s) that don't have any days selected.
              These won't be displayed on any day until you assign days to them.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  
</div>